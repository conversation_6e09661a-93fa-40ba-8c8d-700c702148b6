'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { SearchResult } from '@/app/services/apiService';
import { ResultCard } from '@/components/ResultCard';
import { motion, AnimatePresence } from 'framer-motion';

interface ResultsDisplayProps {
  results: SearchResult[];
  reportId?: string;
  isRedacted?: boolean;
  onUnlock?: () => void;
}

export default function ResultsDisplay({
  results,
  reportId,
  isRedacted = false,
  onUnlock = () => {}
}: ResultsDisplayProps) {
  const [visibleItemsByCategory, setVisibleItemsByCategory] = useState<{[key: string]: number}>({
    certain: 6,
    confident: 6,
    uncertain: 6,
    weak: 6
  });

  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Extract domains for filtering later (if needed)
  const extractDomain = (url: string): string => {
    if (isRedacted && url === "REDACTED-URL") {
      return "Redacted";
    }
    
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      
      // For well-known sites, return the platform name instead of the domain
      if (domain.includes('facebook.com')) return 'Facebook';
      if (domain.includes('instagram.com')) return 'Instagram';
      if (domain.includes('twitter.com') || domain.includes('x.com')) return 'Twitter';
      if (domain.includes('linkedin.com')) return 'LinkedIn';
      if (domain.includes('tiktok.com')) return 'TikTok';
      if (domain.includes('pinterest.com')) return 'Pinterest';
      if (domain.includes('youtube.com') || domain.includes('youtu.be')) return 'YouTube';
      if (domain.includes('reddit.com')) return 'Reddit';
      
      return domain;
    } catch {
      return 'Unknown';
    }
  };
  
  // Categorize results by confidence score
  const categorizedResults = {
    certain: results.filter(r => r.confidence >= 90), // 90-100: Certain Match (dark green)
    confident: results.filter(r => r.confidence >= 83 && r.confidence < 90), // 83-89: Confident Match (light green)
    uncertain: results.filter(r => r.confidence >= 70 && r.confidence < 83), // 70-82: Uncertain Match (yellow)
    weak: results.filter(r => r.confidence >= 50 && r.confidence < 70) // 50-69: Weak Match (red)
  };

  // Get filtered results based on selected category
  const getFilteredResults = () => {
    if (selectedCategory === 'all') {
      return results;
    }
    return categorizedResults[selectedCategory as keyof typeof categorizedResults] || [];
  };

  // Load more results for a specific category
  const loadMoreForCategory = (category: string) => {
    setVisibleItemsByCategory(prev => ({
      ...prev,
      [category]: prev[category] + 6 // Load 6 more (2 more rows of 3 items)
    }));
  };

  // Category filter options
  const categoryOptions = [
    { key: 'all', label: 'All Results', count: results.length, color: 'bg-gray-500' },
    { key: 'certain', label: 'Certain Match', count: categorizedResults.certain.length, color: 'bg-green-600' },
    { key: 'confident', label: 'Confident Match', count: categorizedResults.confident.length, color: 'bg-green-500' },
    { key: 'uncertain', label: 'Uncertain Match', count: categorizedResults.uncertain.length, color: 'bg-amber-500' },
    { key: 'weak', label: 'Weak Match', count: categorizedResults.weak.length, color: 'bg-red-500' }
  ].filter(option => option.count > 0); // Only show categories with results
  
  // Check if a result potentially has red flags (based on confidence, domain, etc.)
  const hasRedFlags = (result: SearchResult): boolean => {
    // Consider a result with low confidence and certain domains as potentially suspicious
    const suspiciousDomains = ['anonymous', 'darknet', 'black', 'hack', 'leak', 'private'];
    
    // Check if the URL contains any suspicious keywords
    const containsSuspiciousKeywords = suspiciousDomains.some(keyword => 
      result.sourceUrl.toLowerCase().includes(keyword)
    );
    
    // Flag results with very low confidence
    const hasLowConfidence = result.confidence < 60;
    
    // Flag results with unusual domains or other criteria you might want to add
    return containsSuspiciousKeywords || hasLowConfidence;
  };

  // Render a category card - hide if empty
  const renderCategoryCard = (
    category: string, 
    items: SearchResult[], 
    title: string, 
    color: string,
    bgColor: string,
    borderColor: string,
    isWeakCategory: boolean = false
  ) => {
    // Don't render anything if there are no items in this category
    if (items.length === 0) return null;
    
    // For weak matches, limit to 10 max
    const maxWeakItems = isWeakCategory ? 10 : Infinity;
    const effectiveVisibleItems = Math.min(
      visibleItemsByCategory[category], 
      isWeakCategory ? Math.min(items.length, maxWeakItems) : items.length
    );
    
    const visibleItems = items.slice(0, effectiveVisibleItems);
    const hasMore = items.length > effectiveVisibleItems && !(isWeakCategory && effectiveVisibleItems >= maxWeakItems);
    const reachedWeakLimit = isWeakCategory && effectiveVisibleItems >= maxWeakItems && items.length > maxWeakItems;
    
    return (
      <div className={`mb-8 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-md overflow-hidden ${borderColor}`}>
        <div className={`${bgColor} p-4 flex justify-between items-center`}>
          <h2 className={`text-lg font-bold ${color}`}>
            {title} ({items.length})
          </h2>
          {isWeakCategory && items.length > 10 && (
            <span className="text-xs px-2 py-1 bg-white/20 rounded-full text-white">
              Limited to 10 max
            </span>
          )}
        </div>
        
        <div className="p-4">
          {visibleItems.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {visibleItems.map(result => (
                <ResultCard 
                  key={result.id} 
                  result={result} 
                  isPrivate={isRedacted} 
                  onUnlockRequest={onUnlock} 
                />
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 py-12">
              <p>No matches found in this category</p>
            </div>
          )}
          
          {/* Load more button */}
          {hasMore && (
            <div className="mt-4 text-center">
              <button
                onClick={() => loadMoreForCategory(category)}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors"
              >
                {isWeakCategory 
                  ? `View More (${Math.min(10 - effectiveVisibleItems, items.length - effectiveVisibleItems)} max of ${items.length - effectiveVisibleItems} remaining)`
                  : `View More (${items.length - effectiveVisibleItems} remaining)`
                }
              </button>
            </div>
          )}
          
          {/* Weak matches limit indicator */}
          {reachedWeakLimit && (
            <div className="mt-4 text-center text-sm text-red-500 dark:text-red-400">
              Maximum weak matches shown (10 of {items.length})
            </div>
          )}
          
          {/* All loaded indicator */}
          {!hasMore && !reachedWeakLimit && items.length > 6 && (
            <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
              ✓ Loading Complete - All matches displayed
            </div>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-6">
      {/* Enhanced Results Header */}
      <div className="mb-8">
        <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
          {/* Redacted warning banner */}
          {isRedacted && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 mb-6 rounded-lg border border-yellow-200 dark:border-yellow-800/50">
              <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                <div className="flex items-center">
                  <div className="mr-4 text-yellow-500">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-yellow-800 dark:text-yellow-200">Locked Report</h3>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                      Preview mode - URLs and source details are redacted
                    </p>
                  </div>
                </div>

                <button
                  onClick={onUnlock}
                  className="bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white font-bold py-2 px-5 rounded-lg shadow-md hover:shadow-lg transition-all"
                >
                  Unlock Full Results
                </button>
              </div>
            </div>
          )}

          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            {/* Left side - Summary stats */}
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Search Results
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Found {results.length} potential matches across the web
              </p>

              {/* Category Filter Tabs */}
              <div className="flex flex-wrap gap-2 mb-4">
                {categoryOptions.map((option) => (
                  <button
                    key={option.key}
                    onClick={() => setSelectedCategory(option.key)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                      selectedCategory === option.key
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <div className={`w-3 h-3 rounded-full ${option.color}`}></div>
                    {option.label}
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      selectedCategory === option.key
                        ? 'bg-white/20 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                    }`}>
                      {option.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
            <span className="text-gray-700 dark:text-gray-300 text-sm">Total Results</span>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{results.length}</p>
          </div>
          
          <div className="md:col-span-2 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <span className="text-blue-700 dark:text-blue-300 text-sm">Relevant Results (≥70%)</span>
            <p className="text-2xl font-bold text-blue-800 dark:text-blue-400">
              {categorizedResults.certain.length + categorizedResults.confident.length + categorizedResults.uncertain.length}
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {Math.round(((categorizedResults.certain.length + categorizedResults.confident.length + categorizedResults.uncertain.length) / results.length) * 100)}% of all results
            </p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <span className="text-green-700 dark:text-green-300 text-sm">Certain Matches</span>
            <p className="text-2xl font-bold text-green-800 dark:text-green-400">{categorizedResults.certain.length}</p>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">90-100%</p>
          </div>
          
          <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg">
            <span className="text-emerald-700 dark:text-emerald-300 text-sm">Confident & Uncertain</span>
            <p className="text-2xl font-bold text-emerald-800 dark:text-emerald-400">
              {categorizedResults.confident.length + categorizedResults.uncertain.length}
            </p>
            <p className="text-xs text-emerald-600 dark:text-emerald-400 mt-1">70-89%</p>
          </div>
        </div>
      </div>
      
      {/* About Match Quality Section - Collapsible */}
      <div className="mb-6 rounded-xl border border-blue-100 dark:border-blue-900/30 overflow-hidden">
        <details className="group" open>
          <summary className="flex justify-between items-center cursor-pointer p-4 bg-blue-50 dark:bg-blue-900/10">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">About Match Scores</h3>
            <svg className="w-5 h-5 text-gray-500 dark:text-gray-400 transform group-open:rotate-180 transition-transform" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </summary>
          
          <div className="p-4 bg-blue-50/50 dark:bg-blue-900/5">
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
              The match quality score ranges from 50-100 and indicates how closely the face matches. A score of 100 means the exact same photo was found online.
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              <span className="font-medium text-green-700 dark:text-green-400">90-100% (Certain):</span> Very high confidence match, likely the same person in a similar pose or the same image.
              <br/>
              <span className="font-medium text-green-600 dark:text-green-400">83-89% (Confident):</span> High confidence match, likely the same person with different lighting or angle.
              <br/>
              <span className="font-medium text-yellow-600 dark:text-yellow-400">70-82% (Uncertain):</span> Moderate confidence, possible match but requires verification.
              <br/>
              <span className="font-medium text-red-600 dark:text-red-400">50-69% (Weak):</span> Low confidence, may be similar looking but likely not the same person.
            </p>
          </div>
        </details>
      </div>
      
      {/* Enhanced Confidence indicator bar with detailed legend */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Match Distribution</h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">Only showing distribution for matches ≥70%</span>
        </div>
        
        {/* Only consider results with ≥70% confidence for distribution */}
        {(() => {
          const relevantResults = categorizedResults.certain.length + categorizedResults.confident.length + categorizedResults.uncertain.length;
          
          return (
            <div className="h-5 flex rounded-full overflow-hidden shadow-sm">
              <div className="bg-green-600 relative" style={{ 
                width: relevantResults > 0 ? `${(categorizedResults.certain.length / relevantResults) * 100}%` : '0',
                minWidth: categorizedResults.certain.length > 0 ? '5%' : '0'
              }}>
                {categorizedResults.certain.length > 0 && (
                  <span className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                    {((categorizedResults.certain.length / relevantResults) * 100).toFixed(0)}%
                  </span>
                )}
              </div>
              <div className="bg-green-400 relative" style={{ 
                width: relevantResults > 0 ? `${(categorizedResults.confident.length / relevantResults) * 100}%` : '0',
                minWidth: categorizedResults.confident.length > 0 ? '5%' : '0'
              }}>
                {categorizedResults.confident.length > 0 && (
                  <span className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                    {((categorizedResults.confident.length / relevantResults) * 100).toFixed(0)}%
                  </span>
                )}
              </div>
              <div className="bg-yellow-400 relative" style={{ 
                width: relevantResults > 0 ? `${(categorizedResults.uncertain.length / relevantResults) * 100}%` : '0',
                minWidth: categorizedResults.uncertain.length > 0 ? '5%' : '0'
              }}>
                {categorizedResults.uncertain.length > 0 && (
                  <span className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                    {((categorizedResults.uncertain.length / relevantResults) * 100).toFixed(0)}%
                  </span>
                )}
              </div>
            </div>
          );
        })()}
        
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
          <span className="relative">
            <span className="absolute -top-4 border-l border-gray-300 h-3"></span>
            70%
          </span>
          <span className="relative">
            <span className="absolute -top-4 border-l border-gray-300 h-3"></span>
            83%
          </span>
          <span className="relative">
            <span className="absolute -top-4 border-l border-gray-300 h-3"></span>
            90%
          </span>
          <span className="relative">
            <span className="absolute -top-4 border-l border-gray-300 h-3"></span>
            100%
          </span>
        </div>
        
        {/* Mini-legend beneath the distribution bar */}
        <div className="flex flex-wrap gap-4 mt-3 justify-center text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-600 rounded-sm mr-1"></div>
            <span>Certain (90-100%)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-400 rounded-sm mr-1"></div>
            <span>Confident (83-89%)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-400 rounded-sm mr-1"></div>
            <span>Uncertain (70-82%)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-sm mr-1"></div>
            <span>Weak (50-69%)</span>
          </div>
        </div>
      </div>
      
      {/* Enhanced Category cards */}
      {renderCategoryCard(
        'certain', 
        categorizedResults.certain, 
        'Certain Matches (90-100%)', 
        'text-green-900 dark:text-green-100',
        'bg-green-100 dark:bg-green-900/30',
        'border-l-6 border-green-600'
      )}
      
      {renderCategoryCard(
        'confident', 
        categorizedResults.confident, 
        'Confident Matches (83-89%)', 
        'text-green-900 dark:text-green-100',
        'bg-green-50 dark:bg-green-900/20',
        'border-l-6 border-green-400'
      )}
      
      {renderCategoryCard(
        'uncertain', 
        categorizedResults.uncertain, 
        'Uncertain Matches (70-82%)', 
        'text-yellow-900 dark:text-yellow-100',
        'bg-yellow-50 dark:bg-yellow-900/20',
        'border-l-6 border-yellow-400'
      )}
      
      {renderCategoryCard(
        'weak', 
        categorizedResults.weak, 
        'Weak Matches (50-69%)', 
        'text-red-900 dark:text-red-100',
        'bg-red-50 dark:bg-red-900/20',
        'border-l-6 border-red-500',
        true // This is the weak category with special limitations
      )}
    </div>
  );
} 