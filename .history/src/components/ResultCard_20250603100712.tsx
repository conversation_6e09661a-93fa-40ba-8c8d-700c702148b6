import React from 'react';
import Image from 'next/image';
import { SearchResult } from '@/app/services/apiService';

interface ResultCardProps {
  result: SearchResult;
  isPrivate: boolean;
  onUnlockRequest: () => void;
}

export function ResultCard({ result, isPrivate, onUnlockRequest }: ResultCardProps) {
  // Extract domain for display
  const extractDomain = (url: string): string => {
    if (isPrivate && url === "REDACTED-URL") {
      return "Redacted"; 
    }
    
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      
      // For well-known sites, return the platform name
      if (domain.includes('facebook.com')) return 'Facebook';
      if (domain.includes('instagram.com')) return 'Instagram';
      if (domain.includes('twitter.com') || domain.includes('x.com')) return 'Twitter';
      if (domain.includes('linkedin.com')) return 'LinkedIn';
      if (domain.includes('tiktok.com')) return 'TikTok';
      if (domain.includes('pinterest.com')) return 'Pinterest';
      if (domain.includes('youtube.com') || domain.includes('youtu.be')) return 'YouTube';
      if (domain.includes('reddit.com')) return 'Reddit';
      
      return domain;
    } catch {
      return 'Unknown';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-100 dark:border-gray-700 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
      <div className="relative aspect-video bg-gray-100 dark:bg-gray-900">
        <Image
          src={result.thumbnail || '/placeholder-image.jpg'}
          alt={result.title || "Search result"}
          fill
          style={{ objectFit: 'cover' }}
          className="rounded-t-lg"
        />

        {/* Confidence Badge - Enhanced with better styling */}
        <div className={`absolute top-3 right-3 px-3 py-1.5 rounded-full text-white text-xs font-bold shadow-lg backdrop-blur-sm ${
          result.confidence >= 90 ? 'bg-green-600/90' :
          result.confidence >= 83 ? 'bg-green-500/90' :
          result.confidence >= 70 ? 'bg-amber-500/90' :
          'bg-red-500/90'
        }`}>
          {result.confidence.toFixed(1)}%
        </div>

        {/* Domain Badge - Enhanced styling */}
        <div className="absolute bottom-3 left-3 bg-black/80 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full font-medium">
          {extractDomain(result.sourceUrl)}
        </div>

        {/* Redacted indicator (only shown when private) */}
        {isPrivate && (
          <div className="absolute top-3 left-3 bg-amber-500/90 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full flex items-center font-medium shadow-lg">
            <svg className="w-3 h-3 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <span>Locked</span>
          </div>
        )}

        {/* Overlay gradient for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"></div>
      </div>
      
      <div className="p-4">
        {/* Title (truncated if private) */}
        <h3 className="text-base font-semibold text-gray-900 mb-2 truncate">
          {isPrivate ? 
            `${result.title?.split(' ')[0] || 'Unknown'}...` : 
            result.title || "Unknown Source"}
        </h3>
        
        {/* Source URL (redacted if private) */}
        <div className="mb-3">
          <span className="text-xs text-gray-500 block mb-1">Source:</span>
          {isPrivate ? (
            <div className="bg-gray-100 px-2 py-1 rounded text-gray-500 text-xs italic">
              URL Redacted - Unlock to view
            </div>
          ) : (
            <a 
              href={result.sourceUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline text-sm truncate block"
            >
              {result.sourceUrl.length > 40 ? result.sourceUrl.substring(0, 40) + '...' : result.sourceUrl}
            </a>
          )}
        </div>
        
        {/* Action Button (unlock or visit source) */}
        <div className="flex justify-end">
          {isPrivate ? (
            <button
              onClick={onUnlockRequest}
              className="inline-flex items-center px-3 py-2 rounded border border-amber-300 bg-amber-50 text-amber-700 text-sm font-medium hover:bg-amber-100"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Unlock
            </button>
          ) : (
            <a
              href={result.sourceUrl}
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 rounded border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Visit Source
            </a>
          )}
        </div>
      </div>
    </div>
  );
} 