@import "tailwindcss";

@layer utilities {
  @keyframes marquee {
    /* Direction: moves right-to-left */
    0% { transform: translateX(0%); }
    100% { transform: translateX(-50%); }
  }
  .animate-marquee {
    animation: marquee 15s linear infinite; /* Faster duration */
  }
  /* Optional: Pause on hover */
  .group-hover\:pause-marquee:hover .animate-marquee {
    animation-play-state: paused;
  }

  /* Enhanced animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Text truncation utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Custom focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 245, 247, 250;
  --background-end-rgb: 255, 255, 255;
  --primary: 41, 100, 221;
  --primary-dark: 34, 83, 184;
}

@theme {
  --color-primary: rgb(var(--primary));
  --color-primary-dark: rgb(var(--primary-dark));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

[data-theme='dark'] {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 18, 18, 18;
  --background-end-rgb: 24, 24, 24;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      rgb(var(--background-start-rgb)),
      rgb(var(--background-end-rgb))
    )
    fixed;
}
